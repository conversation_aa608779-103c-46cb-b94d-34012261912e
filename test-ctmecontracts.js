// // Simple test script to verify CTMecontracts URL parsing
// const { 
//   parseCTMecontractsEmailLink, 
//   isValidCTMecontractsEmailLink 
// } = require('./src/app/common/util/ctmecontractsParser.js');

// // Test URLs
// const sampleSafeLinksUrl = 'https://nam12.safelinks.protection.outlook.com/?url=https%3A%2F%2Fwww.ctmecontracts.com%2Ffiles%2FPDFConvert%2F7626c339-b4a7-4fd3-a32b-dce21d8eef54.pdf%3Ffn%3D2802%2BSundown%2BLN%2B%2B103%2BBoulder%2BCONTRACT%2BTO%2BBUY%2BAND%2BSELL%2BREAL%2BESTATE%2B-%2B%2B%2B%2B%2B%2B%2BResidential.pdf%26dwn%3D1%26ST%3D&data=05%7C02%7Celizabeth.boese%40cbrealty.com%7C99e3f08dfe6241b9930108dd880214d8%7C28743320645e48408154b4babd41162c%7C1%7C1%7C638816264723318600%7CUnknown%7CTWFpbGZsb3d8eyJFbXB0eU1hcGkiOnRydWUsIlYiOiIwLjAuMDAwMCIsIlAiOiJXaW4zMiIsIkFOIjoiTWFpbCIsIldUIjoyfQ%3D%3D%7C0%7C%7C%7C&sdata=q4J1VHSToTCVjhcex6xbKNEIDBbfpmm4XDoVDN59zJY%3D&reserved=0';

// const sampleDirectUrl = 'https://www.ctmecontracts.com/files/PDFConvert/7626c339-b4a7-4fd3-a32b-dce21d8eef54.pdf?fn=2802%2BSundown%2BLN%2B%2B103%2BBoulder%2BCONTRACT%2BTO%2BBUY%2BAND%2BSELL%2BREAL%2BESTATE%2B-%2B%2B%2B%2B%2B%2B%2BResidential.pdf&dwn=1&ST=';

// console.log('Testing CTMecontracts URL Parsing...\n');

// // Test validation
// console.log('1. Testing URL validation:');
// console.log('SafeLinks URL valid:', isValidCTMecontractsEmailLink(sampleSafeLinksUrl));
// console.log('Direct URL valid:', isValidCTMecontractsEmailLink(sampleDirectUrl));
// console.log('Invalid URL valid:', isValidCTMecontractsEmailLink('https://google.com'));
// console.log('');

// // Test parsing
// console.log('2. Testing URL parsing:');
// try {
//   const safeLinksResult = parseCTMecontractsEmailLink(sampleSafeLinksUrl);
//   console.log('SafeLinks parsing result:');
//   console.log('  - Valid:', safeLinksResult.isValid);
//   console.log('  - URL:', safeLinksResult.url.substring(0, 80) + '...');
//   console.log('  - Filename:', safeLinksResult.filename);
//   console.log('');
// } catch (error) {
//   console.log('SafeLinks parsing error:', error.message);
// }

// try {
//   const directResult = parseCTMecontractsEmailLink(sampleDirectUrl);
//   console.log('Direct URL parsing result:');
//   console.log('  - Valid:', directResult.isValid);
//   console.log('  - URL:', directResult.url.substring(0, 80) + '...');
//   console.log('  - Filename:', directResult.filename);
//   console.log('');
// } catch (error) {
//   console.log('Direct URL parsing error:', error.message);
// }

// // Test error handling
// console.log('3. Testing error handling:');
// try {
//   parseCTMecontractsEmailLink('https://google.com');
// } catch (error) {
//   console.log('Invalid URL error (expected):', error.message);
// }

// console.log('\nAll tests completed!');
